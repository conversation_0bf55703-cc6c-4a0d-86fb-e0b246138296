package com.example.demo.Service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Period;
import java.util.*;

@Service
public class ChartService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取眼疾-年龄分布数据
     * @return 包含年龄段和对应眼疾数量的数据
     */
    public Map<String, Object> getEyeDiseaseAgeDistribution() {
        String sql = "SELECT birth_date, righteye_condition, lefteye_condition FROM patients WHERE righteye_condition IS NOT NULL OR lefteye_condition IS NOT NULL";
        
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);
        
        // 定义年龄段
        Map<String, int[]> ageGroups = new LinkedHashMap<>();
        ageGroups.put("0-18", new int[8]);
        ageGroups.put("19-30", new int[8]);
        ageGroups.put("31-45", new int[8]);
        ageGroups.put("46-60", new int[8]);
        ageGroups.put("61+", new int[8]);
        
        // 疾病名称
        List<String> diseaseNames = Arrays.asList(
            "青光眼", "白内障", "视网膜病变", "干眼症", 
            "黄斑变性", "角膜炎", "结膜炎", "屈光不正"
        );
        
        // 统计每个年龄段的疾病分布
        for (Map<String, Object> row : results) {
            String birthDateStr = (String) row.get("birth_date");
            String rightEyeCondition = (String) row.get("righteye_condition");
            String leftEyeCondition = (String) row.get("lefteye_condition");
            
            try {
                // 计算年龄
                LocalDate birthDate = LocalDate.parse(birthDateStr);
                LocalDate currentDate = LocalDate.now();
                int age = Period.between(birthDate, currentDate).getYears();
                
                // 确定年龄段
                String ageGroup;
                if (age <= 18) ageGroup = "0-18";
                else if (age <= 30) ageGroup = "19-30";
                else if (age <= 45) ageGroup = "31-45";
                else if (age <= 60) ageGroup = "46-60";
                else ageGroup = "61+";
                
                // 处理右眼疾病数据
                if (rightEyeCondition != null && !rightEyeCondition.isEmpty()) {
                    // 移除JSON字符串的括号
                    String processed = rightEyeCondition.replace("[", "").replace("]", "");
                    String[] values = processed.split(",");
                    
                    for (int i = 0; i < values.length && i < 8; i++) {
                        if ("1".equals(values[i].trim())) {
                            ageGroups.get(ageGroup)[i]++;
                        }
                    }
                }
                
                // 处理左眼疾病数据
                if (leftEyeCondition != null && !leftEyeCondition.isEmpty()) {
                    // 移除JSON字符串的括号
                    String processed = leftEyeCondition.replace("[", "").replace("]", "");
                    String[] values = processed.split(",");
                    
                    for (int i = 0; i < values.length && i < 8; i++) {
                        if ("1".equals(values[i].trim())) {
                            ageGroups.get(ageGroup)[i]++;
                        }
                    }
                }
            } catch (Exception e) {
                // 处理日期解析异常
                e.printStackTrace();
            }
        }
        
        // 构建返回数据
        Map<String, Object> result = new HashMap<>();
        result.put("diseaseNames", diseaseNames);

        List<String> ageCategories = new ArrayList<>(ageGroups.keySet());
        result.put("ageCategories", ageCategories);

        // 为每种疾病生成一个数组
        Map<String, List<Integer>> diseaseSeries = new LinkedHashMap<>();
        for (int i = 0; i < diseaseNames.size(); i++) {
            List<Integer> countsByAge = new ArrayList<>();
            for (String ageGroup : ageGroups.keySet()) {
                countsByAge.add(ageGroups.get(ageGroup)[i]);
            }
            diseaseSeries.put(diseaseNames.get(i), countsByAge);
        }

        result.put("series", diseaseSeries);
        return result;
    }

    /**
     * 获取眼疾-性别分布数据
     * @return 包含性别和对应眼疾数量的数据
     */
    public Map<String, Object> getEyeDiseaseGenderDistribution() {
        String sql = "SELECT gender, righteye_condition, lefteye_condition FROM patients WHERE righteye_condition IS NOT NULL OR lefteye_condition IS NOT NULL";
        
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);
        
        // 定义性别组
        Map<String, int[]> genderGroups = new LinkedHashMap<>();
        genderGroups.put("男性", new int[8]);
        genderGroups.put("女性", new int[8]);
        
        // 疾病名称
        List<String> diseaseNames = Arrays.asList(
            "青光眼", "白内障", "视网膜病变", "干眼症", 
            "黄斑变性", "角膜炎", "结膜炎", "屈光不正"
        );
        
        // 统计每个性别的疾病分布
        for (Map<String, Object> row : results) {
            String gender = (String) row.get("gender");
            String rightEyeCondition = (String) row.get("righteye_condition");
            String leftEyeCondition = (String) row.get("lefteye_condition");
            
            String genderGroup = "man".equals(gender) ? "男性" : "女性";
            
            // 处理右眼疾病数据
            if (rightEyeCondition != null && !rightEyeCondition.isEmpty()) {
                // 移除JSON字符串的括号
                String processed = rightEyeCondition.replace("[", "").replace("]", "");
                String[] values = processed.split(",");
                
                for (int i = 0; i < values.length && i < 8; i++) {
                    if ("1".equals(values[i].trim())) {
                        genderGroups.get(genderGroup)[i]++;
                    }
                }
            }
            
            // 处理左眼疾病数据
            if (leftEyeCondition != null && !leftEyeCondition.isEmpty()) {
                // 移除JSON字符串的括号
                String processed = leftEyeCondition.replace("[", "").replace("]", "");
                String[] values = processed.split(",");
                
                for (int i = 0; i < values.length && i < 8; i++) {
                    if ("1".equals(values[i].trim())) {
                        genderGroups.get(genderGroup)[i]++;
                    }
                }
            }
        }
        
        // 构建返回数据
        Map<String, Object> result = new HashMap<>();
        result.put("diseaseNames", diseaseNames);
        
        // 为每个性别生成一个数组
        Map<String, List<Integer>> genderSeries = new LinkedHashMap<>();
        for (String genderGroup : genderGroups.keySet()) {
            List<Integer> counts = new ArrayList<>();
            for (int i = 0; i < 8; i++) {
                counts.add(genderGroups.get(genderGroup)[i]);
            }
            genderSeries.put(genderGroup, counts);
        }
        
        result.put("series", genderSeries);
        return result;
    }
}
