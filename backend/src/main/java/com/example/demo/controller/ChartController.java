package com.example.demo.Controller;

import com.example.demo.Service.ChartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/charts")
@CrossOrigin(origins = "*")
public class ChartController {

    @Autowired
    private ChartService chartService;

    /**
     * 获取眼疾-年龄分布数据
     * @return 包含年龄段和对应眼疾数量的数据
     */
    @GetMapping("/disease-age")
    public Map<String, Object> getEyeDiseaseAgeDistribution() {
        return chartService.getEyeDiseaseAgeDistribution();
    }

    /**
     * 获取眼疾-性别分布数据
     * @return 包含性别和对应眼疾数量的数据
     */
    @GetMapping("/disease-gender")
    public Map<String, Object> getEyeDiseaseGenderDistribution() {
        return chartService.getEyeDiseaseGenderDistribution();
    }
}
