package com.example.demo.pojo.patientPojo;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface PatientRepository extends JpaRepository<Patient, Long> {

    /**
     * 使用原生SQL语句自定义插入患者信息。
     * @param patientSn 患者编号
     * @param name 姓名
     * @param gender 性别
     * @param birthDate 出生日期
     * @return 返回受影响的行数，通常为1表示成功。
     */
    @Modifying
    @Transactional
    @Query(value = "INSERT INTO patients (patient_sn, name, gender, birth_date) VALUES (?1, ?2, ?3, ?4)", nativeQuery = true)
    Integer customInsertPatient(Integer patientSn, String name, String gender, String birthDate);

    /**
     * 使用原生SQL查询ID最大的患者记录。
     * @return 返回patient_id最大的Patient对象。
     */
    @Query(value = "SELECT * FROM patients ORDER BY patient_id DESC LIMIT 1", nativeQuery = true)
    Patient findMaxPatientId();

    /**
     * 获取最新且left_eye_img和right_eye_img为空的患者。
     * 注意：字段名必须与Patient实体类属性名一致（区分大小写）。
     * 如果你的Patient实体字段为 left_eye_img 和 right_eye_img（下划线风格），
     * 方法名应如下：
     */
    @Query(value = "SELECT * FROM patients WHERE righteye_condition IS NULL ORDER BY patient_id DESC LIMIT 1", nativeQuery = true)
    Patient findTop();

    /**
     * 获取patient_id最大的患者。
     */
    @Query(value = "SELECT * FROM patients ORDER BY patient_id DESC LIMIT 1", nativeQuery = true)
    Patient findTopByOrderByPatientIdDesc();

    /**
     * 根据ID查找患者
     * 实际对应SQL:
     * SELECT * FROM patients WHERE patient_id = ?;
     */
    Patient findById(long id);

    /**
     * 保存或更新患者
     * 实际对应SQL（JPA自动实现）:
     * 如果id存在:
     *   UPDATE patients SET ... WHERE patient_id = ?;
     * 如果id不存在:
     *   INSERT INTO patients (...) VALUES (...);
     * 使用JpaRepository的save方法即可，无需自定义SQL。
     */
    @Modifying
    @Transactional
    @Query(value = "UPDATE patients SET name = ?1, disease_condition = ?2, disease_grade = ?3, analysis_progress = ?4, diagnosis_date = ?5 WHERE patient_id = ?6", nativeQuery = true)
    void updatePatientInfo(String name, String diseaseCondition, String diseaseGrade, String analysisProgress, String diagnosisDate, Long patientId);

    /**
     * 使用原生SQL批量更新患者信息（仅示例，推荐用JPA的save方法）
     */
    @Modifying
    @Transactional
    @Query(value = "UPDATE patients SET patient_sn = ?1, name = ?2, gender = ?3, birth_date = ?4, left_eye_img = ?5, right_eye_img = ?6, lefteye_condition = ?7, righteye_condition = ?8 WHERE patient_id = ?9", nativeQuery = true)
    void updatePatientAllFields(Integer patientSn, String name, String gender, String birthDate, String leftEyeImg, String rightEyeImg, String lefteyeCondition, String righteyeCondition, Long patientId);
}