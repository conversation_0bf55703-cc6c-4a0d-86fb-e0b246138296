package com.example.demo.Service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class ChartServiceTest {

    @Autowired
    private ChartService chartService;

    @Test
    void getEyeDiseaseAgeDistribution() {
        Map<String, Object> result = chartService.getEyeDiseaseAgeDistribution();
        
        assertNotNull(result);
        assertTrue(result.containsKey("ageCategories"));
        assertTrue(result.containsKey("series"));
        
        System.out.println("年龄分布数据: " + result);
    }

    @Test
    void getEyeDiseaseGenderDistribution() {
        Map<String, Object> result = chartService.getEyeDiseaseGenderDistribution();
        
        assertNotNull(result);
        assertTrue(result.containsKey("diseaseNames"));
        assertTrue(result.containsKey("series"));
        
        System.out.println("性别分布数据: " + result);
    }
}
