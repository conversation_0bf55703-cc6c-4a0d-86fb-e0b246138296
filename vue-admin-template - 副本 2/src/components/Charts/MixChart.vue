<template>
  <div :id="id" :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from './mixins/resize'
import { getEyeDiseaseGenderDistribution } from '@/api/chart'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '200px'
    }
  },
  data() {
    return {
      chart: null,
      chartData: null
    }
  },
  mounted() {
    this.fetchData()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    async fetchData() {
      try {
        const response = await getEyeDiseaseGenderDistribution()
        this.chartData = response.data
        this.initChart()
      } catch (error) {
        console.error('获取性别分布数据失败:', error)
        // 使用默认数据
        this.useDefaultData()
        this.initChart()
      }
    },
    useDefaultData() {
      this.chartData = {
        diseaseNames: ['青光眼', '白内障', '视网膜病变', '干眼症', '黄斑变性', '角膜炎', '结膜炎', '屈光不正'],
        series: {
          '女性': [497, 243, 90, 123, 75, 27, 110, 474],
          '男性': [639, 282, 125, 88, 86, 50, 64, 490]
        }
      }
    },
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))

      // 如果没有数据，使用默认数据
      if (!this.chartData) {
        this.useDefaultData()
      }

      const xData = this.chartData.diseaseNames
      const femaleData = this.chartData.series['女性'] || []
      const maleData = this.chartData.series['男性'] || []

      // 计算平均值
      const averageData = xData.map((_, index) => {
        const female = femaleData[index] || 0
        const male = maleData[index] || 0
        return female + male
      })
      this.chart.setOption({
        backgroundColor: '#344b58',
        title: {
          text: '统计数据',
          x: '20',
          top: '20',
          textStyle: {
            color: '#fff',
            fontSize: '22'
          },
          subtextStyle: {
            color: '#90979c',
            fontSize: '16'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            textStyle: {
              color: '#fff'
            }
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          borderWidth: 0,
          top: 150,
          bottom: 95,
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          x: '5%',
          top: '10%',
          textStyle: {
            color: '#90979c'
          },
          data: ['女性', '男性', '平均']
        },
        calculable: true,
        xAxis: [{
          type: 'category',
          axisLine: {
            lineStyle: {
              color: '#90979c'
            }
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitArea: {
            show: false
          },
          axisLabel: {
            interval: 0,
            rotate: 45, // 旋转标签以防止重叠
            textStyle: {
              color: '#fff'
            }
          },
          data: xData
        }],
        yAxis: [{
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              color: '#90979c'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#90979c'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0,
            textStyle: {
              color: '#fff'
            }
          },
          splitArea: {
            show: false
          }
        }],
        dataZoom: [{
          show: true,
          height: 30,
          xAxisIndex: [
            0
          ],
          bottom: 30,
          start: 10,
          end: 80,
          handleIcon: 'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
          handleSize: '110%',
          handleStyle: {
            color: '#d3dee5'

          },
          textStyle: {
            color: '#fff' },
          borderColor: '#90979c'

        }, {
          type: 'inside',
          show: true,
          height: 15,
          start: 1,
          end: 35
        }],
        series: [{
          name: '女性',
          type: 'bar',
          stack: '总量',
          barMaxWidth: 35,
          barGap: '10%',
          itemStyle: {
            normal: {
              color: 'rgba(255,144,128,1)',
              label: {
                show: true,
                textStyle: {
                  color: '#fff'
                },
                position: 'insideTop',
                formatter(p) {
                  return p.value > 0 ? p.value : ''
                }
              }
            }
          },
          data: femaleData
        },

        {
          name: '男性',
          type: 'bar',
          stack: '总量',
          itemStyle: {
            normal: {
              color: 'rgba(0,191,183,1)',
              barBorderRadius: 0,
              label: {
                show: true,
                position: 'top',
                formatter(p) {
                  return p.value > 0 ? p.value : ''
                }
              }
            }
          },
          data: maleData
        }, {
          name: '平均',
          type: 'line',
          stack: '总量',
          symbolSize: 10,
          symbol: 'circle',
          itemStyle: {
            normal: {
              color: 'rgba(252,230,48,1)',
              barBorderRadius: 0,
              label: {
                show: true,
                position: 'top',
                formatter(p) {
                  return p.value > 0 ? p.value : ''
                }
              }
            }
          },
          data: averageData
        }
        ]
      })
    }
  }
}
</script>
