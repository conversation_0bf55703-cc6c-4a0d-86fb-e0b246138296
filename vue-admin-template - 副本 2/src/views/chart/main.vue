<template>
  <div class="chart-container">
    <h2 class="title">图形化数据分析</h2>
    <div ref="chart" class="chart"></div>
    <div ref="chart2" class="chart"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { getEyeDiseaseAgeDistribution, getEyeDiseaseGenderDistribution } from '@/api/chart'

export default {
  name: "NightingaleChart",
  data() {
    return {
      ageData: [],
      genderData: []
    }
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      try {
        // 获取年龄分布数据
        const ageResponse = await getEyeDiseaseAgeDistribution();
        this.ageData = this.transformAgeData(ageResponse.data);

        // 获取性别分布数据
        const genderResponse = await getEyeDiseaseGenderDistribution();
        this.genderData = this.transformGenderData(genderResponse.data);

        this.initChart();
      } catch (error) {
        console.error('获取图表数据失败:', error);
        // 如果API调用失败，使用默认数据
        this.useDefaultData();
        this.initChart();
      }
    },
    transformAgeData(data) {
      // 将年龄分布数据转换为饼图格式
      const result = [];
      if (data && data.diseaseNames && data.series) {
        data.diseaseNames.forEach((diseaseName, index) => {
          let totalCount = 0;
          // 计算所有年龄段该疾病的总数
          Object.values(data.series).forEach(ageGroupData => {
            totalCount += ageGroupData[index] || 0;
          });
          if (totalCount > 0) {
            result.push({ value: totalCount, name: diseaseName });
          }
        });
      }
      return result;
    },
    transformGenderData(data) {
      // 将性别分布数据转换为饼图格式
      const result = [];
      if (data && data.diseaseNames && data.series) {
        data.diseaseNames.forEach((diseaseName, index) => {
          let totalCount = 0;
          // 计算所有性别该疾病的总数
          Object.values(data.series).forEach(genderData => {
            totalCount += genderData[index] || 0;
          });
          if (totalCount > 0) {
            result.push({ value: totalCount, name: diseaseName });
          }
        });
      }
      return result;
    },
    useDefaultData() {
      // 默认数据作为后备
      this.ageData = [
        { value: 40, name: "正常" },
        { value: 38, name: "糖尿病" },
        { value: 32, name: "青光眼" },
        { value: 30, name: "白内障" },
        { value: 28, name: "AMDM" },
        { value: 26, name: "高血压" },
        { value: 22, name: "近视" }
      ];
      this.genderData = [...this.ageData];
    },
    initChart() {
      // 获取 DOM
      let chartDom = this.$refs.chart;
      let myChart = echarts.init(chartDom);

      let chartDom2 = this.$refs.chart2;
      let myChart2 = echarts.init(chartDom2);

      // 配置项
      let option = {
        legend: {
          top: "bottom",
          bottom: 20, // 向下移动标注选项
        },
        toolbox: {
          show: true,
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            restore: { show: true },
            saveAsImage: { show: true },
          },
        },
        series: [
          {
            name: "Nightingale Chart",
            type: "pie",
            radius: [30, 150], // 调整半径以减少拥挤
            center: ["50%", "45%"], // 向上移动饼图
            roseType: "area",
            itemStyle: {
              borderRadius: 8,
            },
            label: {
              show: true,
              formatter: '{b}: {c}', // 显示名称和数值
            },
            data: this.ageData,
          },
        ],
      };

      // 配置项
      let option2 = {
        legend: {
          top: "bottom",
          bottom: 20, // 向下移动标注选项
        },
        toolbox: {
          show: true,
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            restore: { show: true },
            saveAsImage: { show: true },
          },
        },
        series: [
          {
            name: "Nightingale Chart",
            type: "pie",
            radius: [30, 150], // 调整半径以减少拥挤
            center: ["50%", "45%"], // 向上移动饼图
            roseType: "area",
            itemStyle: {
              borderRadius: 8,
            },
            label: {
              show: true,
              formatter: '{b}: {c}', // 显示名称和数值
            },
            data: this.genderData,
          },
        ],
      };

      // 渲染图表
      myChart.setOption(option);
      myChart2.setOption(option2);

      // 监听窗口大小变化，自动调整图表大小
      window.addEventListener("resize", () => {
        myChart.resize();
        myChart2.resize();
      });
    },
  },
};
</script>

<style scoped>
.chart-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title {
  text-align: center;
  margin-bottom: 20px;
}

.chart {
  width: 600px;
  height: 400px;
  align-self: flex-start;
  margin-top: 40px;
  /* 向下移动 */
  margin-left: 10px;
  /* 向右移动 */
}
</style>
